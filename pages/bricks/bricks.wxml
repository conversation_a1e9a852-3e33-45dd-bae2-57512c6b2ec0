<!--pages/bricks/bricks.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">积木库</view>
    <view class="header-subtitle">管理您的能力积木和简历</view>
  </view>



  <!-- 批量操作栏 -->
  <view class="batch-bar {{isSelectMode ? 'active' : ''}}">
    <view class="batch-text">已选择 {{selectedItems.length}} 项</view>
    <view class="batch-actions">
      <button class="batch-btn cancel-btn" bindtap="cancelSelect">取消</button>
      <button class="batch-btn delete-btn" bindtap="deleteSelected" wx:if="{{selectedItems.length > 0}}">删除</button>
    </view>
  </view>



  <!-- 上传简历区域 -->
  <view class="upload-section">
    <view class="upload-card">
      <view class="upload-icon">📄</view>
      <view class="upload-text">
        <view class="upload-title">上传简历解析</view>
        <view class="upload-desc">上传PDF或Word简历，自动解析为积木库</view>
      </view>
      <button class="upload-btn" bindtap="uploadResume" disabled="{{uploading}}">
        <text wx:if="{{!uploading}}">选择文件</text>
        <text wx:else>解析中...</text>
      </button>
    </view>
  </view>

  <!-- 筛选区域 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-tabs">
        <view class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}"
              bindtap="setFilter" data-filter="all">
          <text class="tab-text">全部</text>
          <text class="tab-count">{{totalCount + resumeCount}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'personal' ? 'active' : ''}}"
              bindtap="setFilter" data-filter="personal">
          <text class="tab-text">个人信息</text>
          <text class="tab-count">{{personalCount}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'education' ? 'active' : ''}}"
              bindtap="setFilter" data-filter="education">
          <text class="tab-text">教育背景</text>
          <text class="tab-count">{{educationCount}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'experience' ? 'active' : ''}}"
              bindtap="setFilter" data-filter="experience">
          <text class="tab-text">工作经历</text>
          <text class="tab-count">{{experienceCount}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'skills' ? 'active' : ''}}"
              bindtap="setFilter" data-filter="skills">
          <text class="tab-text">能力积木</text>
          <text class="tab-count">{{skillCount}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'bricks' ? 'active' : ''}}"
              bindtap="setFilter" data-filter="bricks">
          <text class="tab-text">积木</text>
          <text class="tab-count">{{totalCount}}</text>
        </view>
        <view class="filter-tab {{currentFilter === 'resumes' ? 'active' : ''}}"
              bindtap="setFilter" data-filter="resumes">
          <text class="tab-text">简历</text>
          <text class="tab-count">{{resumeCount}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 积木区域 -->
  <view class="bricks-section">

    <!-- 个人信息模块 -->
    <view class="module-section" wx:if="{{currentFilter === 'all' || currentFilter === 'personal'}}">
      <view class="section-header">
        <view class="section-title">👤 个人信息 ({{personalCount}})</view>
        <view class="section-actions">
          <view class="action-btn" bindtap="addBrick" wx:if="{{!isSelectMode}}" data-category="personal">
            <text class="icon">+</text>
          </view>
        </view>
      </view>
      <view class="bricks-grid">
        <view class="brick-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{filteredBricks}}"
              wx:for-item="item"
              wx:key="id"
              wx:if="{{(currentFilter === 'all' || currentFilter === 'personal') && (item.category === 'personal' || item.category === '个人' || item.category === '个人信息' || item.category === 'personalInfo' || item.type === 'personal')}}">

          <!-- 选择指示器 -->
          <view class="select-indicator" wx:if="{{isSelectMode}}"
                bindtap="toggleSelect"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{item.selected}}">✓</text>
            </view>
          </view>

          <!-- 积木内容 -->
          <view class="brick-content"
                bindtap="{{isSelectMode ? 'toggleSelect' : 'viewBrickDetail'}}"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="brick-body">
              <view class="brick-title">{{item.title}}</view>
              <view class="brick-desc">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 教育背景模块 -->
    <view class="module-section" wx:if="{{currentFilter === 'all' || currentFilter === 'education'}}">
      <view class="section-header">
        <view class="section-title">🎓 教育背景 ({{educationCount}})</view>
        <view class="section-actions">
          <view class="action-btn" bindtap="addBrick" wx:if="{{!isSelectMode}}" data-category="education">
            <text class="icon">+</text>
          </view>
        </view>
      </view>
      <view class="bricks-grid">
        <view class="brick-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{filteredBricks}}"
              wx:for-item="item"
              wx:key="id"
              wx:if="{{(currentFilter === 'all' || currentFilter === 'education') && (item.category === 'education' || item.category === '教育' || item.category === '教育背景' || item.category === 'educationBackground' || item.type === 'education')}}">

          <!-- 选择指示器 -->
          <view class="select-indicator" wx:if="{{isSelectMode}}"
                bindtap="toggleSelect"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{item.selected}}">✓</text>
            </view>
          </view>

          <!-- 积木内容 -->
          <view class="brick-content"
                bindtap="{{isSelectMode ? 'toggleSelect' : 'viewBrickDetail'}}"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="brick-body">
              <view class="brick-title">{{item.title}}</view>
              <view class="brick-desc">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 工作经历模块 -->
    <view class="module-section" wx:if="{{currentFilter === 'all' || currentFilter === 'experience'}}">
      <view class="section-header">
        <view class="section-title">💼 工作经历 ({{experienceCount}})</view>
        <view class="section-actions">
          <view class="action-btn" bindtap="addBrick" wx:if="{{!isSelectMode}}" data-category="experience">
            <text class="icon">+</text>
          </view>
        </view>
      </view>
      <view class="bricks-grid">
        <view class="brick-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{filteredBricks}}"
              wx:for-item="item"
              wx:key="id"
              wx:if="{{(currentFilter === 'all' || currentFilter === 'experience') && (item.category === 'experience' || item.category === '经验' || item.category === '工作经历' || item.category === 'workExperience' || item.category === 'work' || item.type === 'experience')}}">

          <!-- 选择指示器 -->
          <view class="select-indicator" wx:if="{{isSelectMode}}"
                bindtap="toggleSelect"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{item.selected}}">✓</text>
            </view>
          </view>

          <!-- 积木内容 -->
          <view class="brick-content"
                bindtap="{{isSelectMode ? 'toggleSelect' : 'viewBrickDetail'}}"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="brick-body">
              <view class="brick-title">{{item.title}}</view>
              <view class="brick-desc">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 能力积木模块 -->
    <view class="module-section" wx:if="{{currentFilter === 'all' || currentFilter === 'skills' || currentFilter === 'bricks'}}">
      <view class="section-header">
        <view class="section-title">🧱 能力积木 ({{abilityCount}})</view>
        <view class="section-actions">
          <view class="action-btn" bindtap="enterSelectMode" wx:if="{{!isSelectMode}}">
            <text class="icon">✓</text>
          </view>
          <view class="action-btn" bindtap="addBrick" wx:if="{{!isSelectMode}}" data-category="skills">
            <text class="icon">+</text>
          </view>
          <view class="action-btn clear-btn" bindtap="clearAllBricks" wx:if="{{!isSelectMode && abilityCount > 0}}">
            <text class="icon">🗑️</text>
          </view>
        </view>
      </view>

      <view class="bricks-grid">
        <view class="brick-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{filteredBricks}}"
              wx:for-item="item"
              wx:key="id"
              wx:if="{{(currentFilter === 'all' || currentFilter === 'skills' || currentFilter === 'bricks') && (item.category === 'skills' || item.category === '技能' || item.category === '技术能力' || item.category === '技能证书' || item.category === 'project' || item.category === '项目' || item.category === '项目经验' || item.type === 'skills' || item.type === 'project')}}">

          <!-- 选择指示器 -->
          <view class="select-indicator" wx:if="{{isSelectMode}}"
                bindtap="toggleSelect"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{item.selected}}">✓</text>
            </view>
          </view>

          <!-- 积木内容 -->
          <view class="brick-content"
                bindtap="{{isSelectMode ? 'toggleSelect' : 'viewBrickDetail'}}"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="brick-body">
              <view class="brick-title">{{item.title}}</view>
              <view class="brick-desc">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 传统积木列表（兼容旧数据） -->
    <view class="module-section" wx:if="{{currentFilter === 'bricks'}}">
      <view class="section-header">
        <view class="section-title">📦 其他积木 ({{filteredBricks.length - personalCount - educationCount - experienceCount - skillCount}})</view>
        <view class="section-actions">
          <view class="action-btn" bindtap="enterSelectMode" wx:if="{{!isSelectMode}}">
            <text class="icon">✓</text>
          </view>
          <view class="action-btn" bindtap="addBrick" wx:if="{{!isSelectMode}}">
            <text class="icon">+</text>
          </view>
        </view>
      </view>

      <view class="bricks-grid">
        <view class="brick-item {{item.selected ? 'selected' : ''}}"
              wx:for="{{filteredBricks}}"
              wx:for-item="item"
              wx:key="id"
              wx:if="{{item.category !== 'personal' && item.category !== 'education' && item.category !== 'experience' && item.category !== 'skills' && item.category !== '技能' && item.category !== '技术能力' && item.category !== 'project' && item.category !== '项目'}}">

          <!-- 选择指示器 -->
          <view class="select-indicator" wx:if="{{isSelectMode}}"
                bindtap="toggleSelect"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{item.selected}}">✓</text>
            </view>
          </view>

          <!-- 积木内容 -->
          <view class="brick-content"
                bindtap="{{isSelectMode ? 'toggleSelect' : 'viewBrickDetail'}}"
                data-id="{{item.id}}"
                data-type="brick">
            <view class="brick-body">
              <view class="brick-title">{{item.title}}</view>
              <view class="brick-desc">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 简历列表 -->
    <view class="resumes-list" wx:if="{{currentFilter === 'all' || currentFilter === 'resumes'}}">
      <view class="section-header">
        <view class="section-title">📄 我的简历 ({{filteredResumes.length}})</view>
        <view class="section-actions">
          <view class="action-btn" bindtap="enterSelectMode" wx:if="{{!isSelectMode}}">
            <text class="icon">✓</text>
          </view>
          <view class="action-btn" bindtap="createResume" wx:if="{{!isSelectMode}}">
            <text class="icon">📄</text>
          </view>
        </view>
      </view>
      
      <view class="bricks-grid">
        <view class="brick-item {{item.selected ? 'selected' : ''}}" 
              wx:for="{{filteredResumes}}" 
              wx:key="id">
          
          <!-- 选择指示器 -->
          <view class="select-indicator" wx:if="{{isSelectMode}}" 
                bindtap="toggleSelect" 
                data-id="{{item.id}}" 
                data-type="resume">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{item.selected}}">✓</text>
            </view>
          </view>



          <!-- 简历内容 - 点击查看详情 -->
          <view class="brick-content" 
                bindtap="{{isSelectMode ? 'toggleSelect' : 'viewResumeDetail'}}" 
                data-id="{{item.id}}" 
                data-type="resume">
            <view class="brick-header">
              <view class="brick-icon">
                <text class="icon">{{item.icon}}</text>
              </view>
              <view class="brick-tag">{{item.status}}</view>
            </view>
            
            <view class="brick-body">
              <view class="brick-title">{{item.title}}</view>
              <view class="brick-desc">{{item.description}}</view>
            </view>
            
            <view class="brick-footer">
              <view class="brick-stats">
                <view class="stat-item">
                  <text class="stat-icon">📅</text>
                  <text class="stat-text">{{item.updateTime}}</text>
                </view>
                <view class="stat-item">
                  <text class="stat-icon">🎯</text>
                  <text class="stat-text">{{item.company}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 积木详情弹窗 -->
  <view class="brick-detail-modal {{showBrickDetail ? 'show' : ''}}" bindtap="closeBrickDetail">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <view class="modal-title">积木详情</view>
        <view class="modal-close" bindtap="closeBrickDetail">✕</view>
      </view>
      
      <view class="modal-body" wx:if="{{currentBrick}}">
        <view class="detail-section">
          <view class="detail-header">
            <view class="detail-icon">{{currentBrick.icon}}</view>
            <view class="detail-info">
              <view class="detail-title">{{currentBrick.title}}</view>
              <view class="detail-category">{{currentBrick.category}}</view>
            </view>
          </view>
        </view>
        
        <view class="detail-section">
          <view class="section-title">📝 详细描述</view>
          <view class="section-content">{{currentBrick.description}}</view>
        </view>
        
        <view class="detail-section" wx:if="{{currentBrick.keywords && currentBrick.keywords.length > 0}}">
          <view class="section-title">🏷️ 关键词</view>
          <view class="keywords">
            <text class="keyword" wx:for="{{currentBrick.keywords}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
        

      </view>
      
      <view class="modal-footer">
        <button class="btn btn-secondary" bindtap="closeBrickDetail">关闭</button>
        <button class="btn btn-primary" bindtap="editCurrentBrick">编辑</button>
        <button class="btn btn-danger" bindtap="deleteCurrentBrick">删除</button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredBricks.length === 0 && filteredResumes.length === 0}}">
    <view class="empty-icon">📦</view>
    <view class="empty-title">暂无内容</view>
    <view class="empty-desc">开始创建您的第一个积木吧</view>
    <button class="btn btn-primary" bindtap="addBrick">
      <text class="btn-icon">+</text>
      <text>添加积木</text>
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view> 